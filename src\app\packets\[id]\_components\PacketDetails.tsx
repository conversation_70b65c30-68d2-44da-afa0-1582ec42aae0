"use client";

import { useState, useMemo } from "react";
import { Packet, Piece } from "@/types/models";
import { SearchIcon, Shirt, Factory, Siren, ScanBarcode, TriangleAlert, CheckCheck, ArrowLeft, Lock, LockOpen } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import Bar from "@/components/Barcode/BarCode";
import Link from "next/link";
import useGetPacketById from "@/hooks/packets/useGetPacketById";
import useUpdatePacket from "@/hooks/packets/useUpdatePacket";

interface Props {
  packet: Packet;
}

export default function PacketDetails({ packet: initialPacket }: Props) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  // Extract colis ID from the packet
  const colisId = typeof initialPacket.colis === 'object' ? initialPacket.colis._id : initialPacket.colis;

  // Parse QR code to get order number and colis number
  const qrParts = initialPacket.qrCode.split('/');
  const orderNumber = qrParts[0] || '';
  const colisNumber = qrParts.length > 1 ? qrParts[1].replace('C', '') : '';

  // Use hooks for real-time data and updates
  const { packet: latestPacket, loading, error, refetch } = useGetPacketById(initialPacket._id);
  const { toggleBlockPacket, loading: updating } = useUpdatePacket();

  // Use the latest data from the hook if available, otherwise use the initial packet
  const packet = latestPacket || initialPacket;

  // Handle block/unblock toggle
  const handleToggleBlock = async () => {
    try {
      await toggleBlockPacket(packet._id, packet.bloquer);
      // Refresh the packet data from the server
      await refetch();
    } catch (error) {
      console.error('Failed to toggle block status:', error);
    }
  };

  const filteredPieces = useMemo(() => {
    const term = searchTerm.toLowerCase();
    return packet.pieces.filter((piece: any) => {
      const matchesSearch =
        (piece.numero.toString().trim() === term.trim()) || (term.trim() === "");
      const matchesStatus = statusFilter
        ? piece.status === statusFilter
        : true;
      return matchesSearch && matchesStatus;
    });
  }, [searchTerm, packet.pieces, statusFilter]);




  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return { text: "En attente", color: "#FFA70B" };
      case "in_progress":
        return { text: "En cours", color: "#2D9CDB" };
      case "finnishing":
        return { text: "Finitions", color: "#9B51E0" };
      case "completed":
        return { text: "Terminée", color: "#219653" };
      case "faulted":
        return { text: "En défaut", color: "#f60303" };
      case "retouche":
        return { text: "Retouche", color: "#f60303" };
      case "canceled":
        return { text: "Annulée", color: "#D34053" };
      default:
        return { text: "Inconnu", color: "#6B7280" };
    }
  };


  const { text, color } = getStatusLabel(packet.status);
  let IconPacket = Siren;
if (packet.status === "completed") IconPacket = CheckCheck;




  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center gap-2">
        <Link href={`/colis/${colisId}`} className="text-blue-600 hover:text-blue-800">
          <ArrowLeft size={20} />
        </Link>

      </div>
   <Breadcrumb
          pageName={`Paquet N°${packet.numero} - Colis ${colisNumber} - OF ${orderNumber}`}
        />
      <div className="flex flex-wrap items-center justify-between rounded-lg bg-white  pl-8 pr-8  border border-blue-950">
        <p className="flex items-center justify-center font-semibold">
          <Shirt size={25} className="mr-1" />
          <span>Total pièces: {packet.pieces.length}</span>
        </p>

        <p className="flex items-center justify-center font-semibold">
          <Factory size={27} className="mb-1 mr-1" />
          <span>Taille: {packet.size}</span>
        </p>

        <div className="flex max-w-fit items-center justify-center rounded-full px-3.5 py-1 font-bold">
          <ScanBarcode size={35} className="mb-1" />
          <span className="pt-2">
            <Bar value={packet.qrCode} />
          </span>
        </div>
        <div className="flex flex-col justify-center items-center space-y-1 pb-2 pt-2">


        <div className="flex items-center gap-2">
          <div
            className="max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center"
            style={{
              backgroundColor: `${color}14`,
              color: color,
            }}
          >
            <IconPacket style={{ color: color }} size={26} className="mb-1" />
            <span>{text}</span>
          </div>

          {/* Block/Unblock Button */}
          <button
            onClick={handleToggleBlock}
            disabled={updating}
            className={`max-w-fit rounded-full px-3 py-1 font-bold flex justify-center items-center transition-colors ${
              packet.bloquer
                ? 'bg-red-100 text-red-900 hover:bg-red-200'
                : 'bg-green-100 text-green-900 hover:bg-green-200'
            } ${updating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            title={packet.bloquer ? 'Débloquer le paquet' : 'Bloquer le paquet'}
          >
            {packet.bloquer ? (
              <Lock size={20} />
            ) : (
              <LockOpen size={20} />
            )}
          </button>

          {loading && (
            <span className="text-blue-500 text-sm">Chargement...</span>
          )}

          {error && (
            <span className="text-red-500 text-sm">{error}</span>
          )}
        </div>

        {packet.scans.length > 0 && packet.scans.at(-1)?.type && !["debutGM", "finGM","ctrlFinCh","finFinition"].includes(packet.scans.at(-1)!.type) && (
  <div className="max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center bg-blue-100 text-blue-900">

    <span>{packet.scans.at(-1)!.type}</span>
  </div>
)}
        </div>




      </div>

      {/* Filters */}
      <div className="rounded-[10px] bg-white shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card border border-blue-950">
        <div className="flex flex-wrap items-center justify-evenly pb-2 pt-2">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Filtrer pièces par N°"
            className="w-75 rounded-md border border-blue-950 font-bold px-4 py-2"
          />

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-75 rounded-md border border-blue-950 font-bold px-4 py-2 "
          >
           <option value="">Tous les statuts</option>
          <option value="pending">En attente</option>
          <option value="in_progress">En cours</option>
          <option value="finnishing">Finitions</option>
          <option value="completed">Terminée</option>
          <option value="faulted">En défaut</option>
          <option value="canceled">Annulée</option>
          </select>
        </div>
      </div>

      {/* Pieces*/}
      <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
      {filteredPieces.map((piece: any) => {
  const { text, color } = getStatusLabel(piece.status);
  let Icon = Siren;
if (piece.status === "faulted") Icon = TriangleAlert;
if (piece.status === "completed") Icon = CheckCheck;


  return (
    <div
      key={piece._id}
      className="rounded-xl bg-white p-4 shadow-md border"
      style={{ borderColor: color, borderWidth: 1, borderStyle: "solid" }}
    >
      <div className="text-center space-y-2">
        <p className="font-semibold">Pièce N° {piece.numero}</p>
        <div
          className="inline-flex items-center rounded-full px-3 py-1 font-bold text-sm"
          style={{
            backgroundColor: `${color}20`, // faded background                   piece status commented for now need to be updated with packet
            color,
          }}
        >
          <Icon size={20} className="mr-1" style={{ color }} />
          {text}
        </div>
        {piece?.defaut && piece.defaut.length > 0 ? (
          <div className="mt-2">
            <p className="font-semibold text-red-600 mb-1">Défauts:</p>
            <ul className="text-left list-disc pl-5">
              {piece.defaut.map((defaut: string, index: number) => (
                <li key={index} className="text-red-600">{defaut}</li>
              ))}
            </ul>
          </div>
        ) : null}
      </div>
    </div>
  );
})}

      </div>
    </div>
  );
}
