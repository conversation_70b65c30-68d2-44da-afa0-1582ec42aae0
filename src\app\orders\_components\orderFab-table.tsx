"use client";

import { TrashIcon } from "@/assets/icons";
import Bar from "@/components/Barcode/BarCode";
import UnifiedPrintMenu from "@/components/Barcode/UnifiedPrintMenu";
import { DownloadIcon, PreviewIcon } from "@/components/Tables/icons";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import useDeleteOrder from "@/hooks/orders/useDeleteOrder";
import useGetOrders from "@/hooks/orders/useGetOrders";
import useUpdateOrder from "@/hooks/orders/useUpdateOrder";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import { Calendar, Clock, Lock, LockOpen, Printer } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

 const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return { text: "En attente", color: "#FFA70B" };
      case "in_progress":
        return { text: "En cours", color: "#2D9CDB" };
      case "finnishing":
        return { text: "Finitions", color: "#9B51E0" };
      case "completed":
        return { text: "Terminée", color: "#219653" };
      case "faulted":
        return { text: "En défaut", color: "#f60303" };
      case "retouche":
        return { text: "Retouche", color: "#f60303" };
      case "canceled":
        return { text: "Annulée", color: "#D34053" };
      default:
        return { text: "Inconnu", color: "#6B7280" };
    }
  };

export function OrderFabTable() {
  var { orders, loading, error,refetch } = useGetOrders();
  const router = useRouter();

  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [chaineFilter, setChaineFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [showPrintMenu, setShowPrintMenu] = useState(false);
  const [selectedOrderForPrint, setSelectedOrderForPrint] = useState<any>(null);

  const { deleteOrder, loading: deleting, error: deleteError } = useDeleteOrder();
  const { toggleBlockOrder, loading: updating } = useUpdateOrder();

  // Handle block/unblock toggle
  const handleToggleBlock = async (orderId: string, currentBlockedState: boolean) => {
    try {
      await toggleBlockOrder(orderId, currentBlockedState);
      await refetch(); // Refresh the orders list
    } catch (error) {
      console.error('Failed to toggle block status:', error);
    }
  };

  const filteredOrders = useMemo(() => {
    return orders?.filter((order: any) => {
      const matchesSearch = (order.orderNumber.toString()===search)||(search.trim()==='') ;
      const matchesStatus = statusFilter ? order.status === statusFilter : true;
      const matchesChaine = chaineFilter ? order.chaine === chaineFilter : true;
      const matchesDate = dateFilter
        ? dayjs(order.createdAt).format("YYYY-MM-DD") === dateFilter
        : true;

      return matchesSearch && matchesStatus && matchesChaine && matchesDate;
    });
  }, [orders, search, statusFilter, chaineFilter, dateFilter]);

  const uniqueChaines = Array.from(new Set(orders?.map((o: any) => o.chaine))).filter(Boolean);

  if (loading) return <div>Chargement...</div>;
  if (error) return <div>Erreur lors du chargement des commandes</div>;

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5">

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <input
          type="text"
          placeholder="Rechercher par N°"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="px-4 py-2 border rounded-md w-48"
        />

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border rounded-md w-48"
        >
          <option value="">Tous les statuts</option>
          <option value="pending">En attente</option>
          <option value="in_progress">En cours</option>
          <option value="finnishing">Finitions</option>
          <option value="completed">Terminée</option>
          <option value="canceled">Annulée</option>
        </select>

        <select
          value={chaineFilter}
          onChange={(e) => setChaineFilter(e.target.value)}
          className="px-4 py-2 border rounded-md w-48"
        >
          <option value="">Toutes les chaînes</option>
          {uniqueChaines.map((chaine) => (
            <option key={chaine} value={chaine}>
              {chaine}
            </option>
          ))}
        </select>

        <input
          type="date"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="px-4 py-2 border rounded-md w-48"
        />
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
            <TableHead className="min-w-[155px] xl:pl-7.5">Ordre De Fabrication</TableHead>
            <TableHead>Article</TableHead>
            <TableHead>Pieces</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Chaîne</TableHead>
            <TableHead>Barcode</TableHead>
            <TableHead className="text-right xl:pr-7.5">Actions</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {filteredOrders?.map((order: any, index: any) => {
            const { text, color } = getStatusLabel(order.status);

            return (
              <TableRow key={order._id || index} className="border-[#eee] dark:border-dark-3">
                <TableCell className="min-w-[155px] xl:pl-7.5">
                  <h5 className="text-dark dark:text-white">
                    <strong>N°{order.orderNumber}</strong>
                  </h5>
                  <p className="mt-[3px] text-body-sm font-medium">
                    {order.colis?.length || 0} colis
                  </p>
                </TableCell>

                <TableCell>
                  <div className="flex flex-col">
                    <span>
                    {order.article.ref}
                    </span>
                    <span>
                    {order.article.model}
                    </span>
                  </div>
                  </TableCell>

                <TableCell>{order.totalPieces}</TableCell>

                <TableCell>
                <div className="flex items-center space-x-2 text-sm text-gray-600">

                  <div className="flex items-center">
                    <Calendar/>
                  <span>{dayjs(order.createdAt).format("DD MMM YYYY")}</span>
                  </div>

                  <div className="flex items-center">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>{dayjs(order.createdAt).format("HH:mm")}H</span>
                  </div>

</div>
                </TableCell>

                <TableCell>
                  <div className="flex flex-col gap-1">
                    <div
                      className="max-w-fit rounded-full px-3.5 py-1 text-sm font-medium"
                      style={{
                        backgroundColor: `${color}14`,
                        color: color,
                      }}
                    >
                      {text}
                    </div>
                    {/* Blocked Status */}
                    {order.bloquer && (
                      <div className="max-w-fit rounded-full px-2 py-1 text-xs font-medium flex items-center gap-1 bg-red-100 text-red-900">
                        <Lock size={12} />
                        Bloqué
                      </div>
                    )}
                  </div>
                </TableCell>

                <TableCell>{order.chaine}</TableCell>
                <TableCell><Bar value={order.qrCode}/></TableCell>

                <TableCell className="xl:pr-7.5">
                  <div className="flex items-center justify-end gap-x-3.5">
                  <Link
  key={order._id}
  href={`/orders/${order._id}`}
  className="hover:text-primary"
>
  <span className="sr-only">Voir</span>
  <PreviewIcon className="size-6 dark:text-dark-7 " />
</Link>

                    {/* Block/Unblock Button */}
                    <button
                      className={`hover:opacity-80 disabled:opacity-50 ${
                        order.bloquer ? 'text-red-500' : 'text-green-500'
                      }`}
                      onClick={() => handleToggleBlock(order._id, order.bloquer)}
                      disabled={updating}
                      title={order.bloquer ? 'Débloquer la commande' : 'Bloquer la commande'}
                    >
                      <span className="sr-only">
                        {order.bloquer ? 'Débloquer' : 'Bloquer'}
                      </span>
                      {order.bloquer ? (
                        <Lock className="size-6" />
                      ) : (
                        <LockOpen className="size-6" />
                      )}
                    </button>

                    <button
  className="hover:text-primary disabled:opacity-50"
  onClick={() => {
    setSelectedOrderId(order._id);
    setShowDeleteModal(true);
  }}
  disabled={deleting}
>
  <span className="sr-only">Supprimer</span>
  <TrashIcon className="text-red-500 size-6" />
</button>



                    <button
                      className="hover:text-primary"
                      onClick={() => {
                        setSelectedOrderForPrint(order);
                        setShowPrintMenu(true);
                      }}
                      title="Imprimer les codes-barres"
                    >
                      <span className="sr-only">Imprimer</span>
                      <Printer className="size-6" />
                    </button>

                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      {/* delte model */}
      {showDeleteModal && (
  <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full">
      <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Confirmation</h2>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Voulez-vous vraiment supprimer cet ordre de fabrication ?
      </p>
      <div className="flex justify-end gap-4">
        <button
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          onClick={() => {
            setShowDeleteModal(false);
            setSelectedOrderId(null);
          }}
        >
          Annuler
        </button>
        <button
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          disabled={deleting}
          onClick={async () => {
            if (selectedOrderId) {
              await deleteOrder(selectedOrderId);
              setShowDeleteModal(false);
              setSelectedOrderId(null);
              await refetch(); // refresh data immediately
            }
          }}
        >
          Supprimer
        </button>
      </div>
    </div>
  </div>
)}

      {/* Unified Print Menu */}
      {showPrintMenu && selectedOrderForPrint && (
        <UnifiedPrintMenu
          order={selectedOrderForPrint}
          isOpen={showPrintMenu}
          onClose={() => {
            setShowPrintMenu(false);
            setSelectedOrderForPrint(null);
          }}
        />
      )}

    </div>

  );
}
