// Common types for models used throughout the application

export interface Piece {
  _id: string;
  numero: number;
  status: string;
  defaut: string[];
}

export interface Packet {
  _id: string;
  numero: number;
  size: string;
  status: string;
  bloquer: boolean; // Indicates if the packet is blocked
  colis: string | Colis; // Reference to parent Colis
  pieces: Piece[] | string[];
  qrCode: string;
  scans: Scan[];
}

export interface Colis {
  _id: string;
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  status: string;
  problems: string[];
  control?: 'Conforme' | 'ANC' | 'Bloque'; // Control status
  order: string | Order; // Reference to parent Order
  packets: Packet[] | string[];
  qrCode: string;
  scans: Scan[];
}

export interface Order {
  _id: string;
  orderNumber: string;
  totalProductionTimeInMinutes: number;
  status: string;
  bloquer: boolean; // Indicates if the order is blocked
  colis: Colis[] | string[]; // References to Colis
  qrCode: string;
  chaine: string;
  totalPieces: number;
  scans: Scan[];
  article: any; // This should be more specific if possible
  createdAt: string;
  updatedAt: string;
}

export interface Scan {
  type: string;
  time: string;
  user?: string;
}

// Data structures for creating new entities

export interface ColisData {
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  piecesPerPacket: number;
}

export interface CreateOrderData {
  orderNumber: string;
  chaine: string;
  totalPieces: number;
  colisData: ColisData[];
  article: string;
}
